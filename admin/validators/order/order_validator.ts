import vine from '@vinejs/vine'

export const createOrderValidator = vine.compile(vine.object({}))

export const updateOrderValidator = vine.compile(
  vine.object({
    name: vine.string(),
    email: vine.string().email(),

    userId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        const user = await query.from('zn_users').where('id', field).first()
        return !!user
      })
      .optional(),

    // orderDetails: vine.array(
    //     vine
    // ),

    billingId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        const address = await query.from('zn_addresses').where('id', field).first()
        return !!address
      })
      .optional()
      .requiredIfMissing('billing'),

    billing: vine.any().optional().requiredIfMissing('billingId'),

    shippingId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        const address = await query.from('zn_addresses').where('id', field).first()
        return !!address
      })
      .optional()
      .requiredIfMissing(['shipping', 'sameBillShip']),
    shipping: vine.any().optional().requiredIfMissing(['shippingId', 'sameBillShip']),

    sameBillShip: vine.boolean().optional().requiredIfMissing(['shippingId', 'shipping']),

    financialStatus: vine.enum(['paid', 'partially_refunded', 'refunded']),

    fulfillmentStatus: vine.enum(['fulfilled', 'partially_fulfilled', 'unfulfilled']),

    subtotalPrice: vine.number(),
    totalDiscounts: vine.number(),
    totalShipping: vine.number(),
    totalTax: vine.number(),
    totalPrice: vine.number(),
  })
)

export const cancelOrderValidator = vine.compile(
  vine.object({
    reason: vine.string(),
  })
)
