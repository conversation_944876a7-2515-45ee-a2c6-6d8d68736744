import ProductRecommender from "#services/chatbot/recommendation/product_recommender";
import CollectionRecommender from "#services/chatbot/recommendation/collection_recommender";
import PostRecommeder from "#services/chatbot/recommendation/post_recommeder";
import RecommendationFitEvaluator from "./recommendation_fit_evaluator.js";
import SparePostRecommender from "./spare_post_recommender.js";

export interface VectorDatabaseMatch {
  id: string
  score: number
  metadata: Record<string, any>
}

export interface EmbeddingService {
  query(text: string, k: number, filter?: any): Promise<any[]>
}

export interface Recommender {
  query(sentence: string, k: number, constraints?: any): Promise<VectorDatabaseMatch[]>
  evaluate?(sentence: string, metadata: any): Promise<boolean>
}

export type RecommendationKind = 'product' | 'collection' | 'post' | 'search_post'

const fitEvaluator = new RecommendationFitEvaluator()

const RECOMMENDER_REGISTRY: Record<RecommendationKind, Recommender> = {
  product: {
    query: (description, k, constraints) => new ProductRecommender().query(description,k, constraints ?? {}),
    evaluate :  (description: string, metadata: { description: string })    => fitEvaluator.isFit(description, metadata.description),
  },
  collection: {
    query: (description, k)    => new CollectionRecommender().query(description, k),
    evaluate :  async ()  => true,
  },
  post : {
    query: (description, k) => new PostRecommeder().query(description, k),
    evaluate :  (description: string, metadata: { description: string })    => fitEvaluator.isFit(description, metadata.description),
  },
  search_post : {
    query: (description, k) => new SparePostRecommender().query(description, k),
  }
}

export interface RecommendationEngineInput {
  descriptions: Partial<Record<RecommendationKind, string | string[]>>
  constraints?: Partial<Record<RecommendationKind, Record<string, any>>>
  maxTotal?: number
}
export type RecommendationEngineOutput = Record<RecommendationKind, string[]>
export default class RecommendationEngine {

  constructor(
   private DEFAULT_MAX_TOTAL = 10
  ) {
  }

  private async gatherSuggestionsIds(
    descriptions: string[],
    perDescQuota: number,
    maxTotal: number,
    recommender: Recommender,
    checkRecommendation: boolean,
    constraints?: any,
  ): Promise<string[]> {

    const chosen: string[] = []

    for (const description of descriptions) {
      if (chosen.length > maxTotal) break
      console.time(`query maxTotal=${maxTotal}`)

      const matches = await recommender.query(description, perDescQuota + 1, constraints)
      console.timeEnd(`query maxTotal=${maxTotal}`)

      let numberOfAddedRecommendation = 0

      console.log('Potential matches length :', matches.length)

      for (const match of matches) {
        if (chosen.includes(match.id)) continue
        if (checkRecommendation && recommender.evaluate && !(await recommender.evaluate(description, match.metadata))) continue
        chosen.push(match.id)
        numberOfAddedRecommendation++
        if (numberOfAddedRecommendation >= perDescQuota || chosen.length >= maxTotal) break
      }
      console.log('Chosen length:', chosen.length)
    }
    return chosen
  }

  async run(input: RecommendationEngineInput,runPostEvaluation : boolean = true): Promise<RecommendationEngineOutput> {
    const maxTotal = input.maxTotal ?? this.DEFAULT_MAX_TOTAL
    const output : RecommendationEngineOutput = Object.create(null)

    for (const [kind, recommender] of Object.entries(RECOMMENDER_REGISTRY) as [RecommendationKind, Recommender][]) {
      const descriptions = this.toArray(input.descriptions?.[kind])
      console.log('Descriptions:', descriptions, 'kind:', kind)
      if (descriptions.length < 1) continue
      console.time('get suggestions')
      output[kind] = await this.gatherSuggestionsIds(
        descriptions,
        this.getQuotaForDescription(descriptions.length, maxTotal),
        maxTotal,
        recommender,
        runPostEvaluation,
        input.constraints?.[kind],
      )
      console.timeEnd('get suggestions')
    }

    return output
  }

  private toArray  (x?: string | string[]) {
    return !x ? [] : Array.isArray(x) ? x : [x]
  }

  private getQuotaForDescription(numberDescriptions: number, maxTotal: number) {
    return Math.max(Math.ceil(maxTotal / Math.max(1, numberDescriptions)), 1)
  }
}
